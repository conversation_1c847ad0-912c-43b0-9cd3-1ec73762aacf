// User and Authentication Types
export interface User {
  id: string;
  username: string;
  role: UserRole;
  shopId?: string;
  name: string;
  email?: string;
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
}

export type UserRole = 'admin' | 'shopA' | 'shopB' | 'shopC';

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

// Shop Types
export interface Shop {
  id: string;
  name: string;
  code: string;
  address: string;
  phone: string;
  managerId: string;
  isActive: boolean;
  createdAt: Date;
}

// Employee Types
export interface Employee {
  id: string;
  name: string;
  shopId: string;
  position: string;
  dailyWage: number;
  isActive: boolean;
  hireDate: Date;
  phone?: string;
  address?: string;
}

export interface Attendance {
  id: string;
  employeeId: string;
  shopId: string;
  date: Date;
  status: AttendanceStatus;
  hoursWorked?: number;
  notes?: string;
}

export type AttendanceStatus = 'present' | 'absent' | 'half-day' | 'overtime';

// Inventory Types
export interface InventoryItem {
  id: string;
  name: string;
  category: string;
  shopId: string;
  quantity: number;
  unitCost: number;
  sellingPrice: number;
  minStockLevel: number;
  supplier?: string;
  lastUpdated: Date;
  createdAt: Date;
}

export interface StockMovement {
  id: string;
  itemId: string;
  shopId: string;
  type: StockMovementType;
  quantity: number;
  unitCost?: number;
  reason: string;
  date: Date;
  userId: string;
}

export type StockMovementType = 'purchase' | 'sale' | 'adjustment' | 'transfer' | 'return';

// Financial Types
export interface Transaction {
  id: string;
  shopId: string;
  type: TransactionType;
  category: string;
  amount: number;
  paymentMethod: PaymentMethod;
  description: string;
  date: Date;
  userId: string;
  receiptNumber?: string;
}

export type TransactionType = 'income' | 'expense';
export type PaymentMethod = 'cash' | 'card' | 'transfer' | 'check' | 'digital';

export interface DailyCash {
  id: string;
  shopId: string;
  date: Date;
  openingBalance: number;
  closingBalance: number;
  totalSales: number;
  totalExpenses: number;
  staffCosts: number;
  physicalCount?: number;
  variance?: number;
  notes?: string;
  userId: string;
}

// Sales Types
export interface Sale {
  id: string;
  shopId: string;
  items: SaleItem[];
  totalAmount: number;
  paymentMethod: PaymentMethod;
  customerId?: string;
  date: Date;
  userId: string;
  receiptNumber: string;
}

export interface SaleItem {
  itemId: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

// Purchase Types
export interface Purchase {
  id: string;
  shopId: string;
  supplierId: string;
  items: PurchaseItem[];
  totalAmount: number;
  paymentStatus: PaymentStatus;
  deliveryDate: Date;
  orderDate: Date;
  userId: string;
  invoiceNumber?: string;
}

export interface PurchaseItem {
  itemId: string;
  quantity: number;
  unitCost: number;
  totalCost: number;
}

export type PaymentStatus = 'pending' | 'partial' | 'paid' | 'overdue';

// Audit Types
export interface Audit {
  id: string;
  shopId: string;
  type: AuditType;
  scheduledDate: Date;
  completedDate?: Date;
  status: AuditStatus;
  items: AuditItem[];
  userId: string;
  notes?: string;
}

export interface AuditItem {
  itemId: string;
  systemCount: number;
  physicalCount: number;
  variance: number;
  reason?: string;
}

export type AuditType = 'weekly' | 'monthly' | 'quarterly' | 'annual' | 'spot';
export type AuditStatus = 'scheduled' | 'in-progress' | 'completed' | 'cancelled';

// Dashboard Types
export interface DashboardStats {
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;
  profitMargin: number;
  totalEmployees: number;
  activeShops: number;
  lowStockItems: number;
  pendingAudits: number;
}

export interface ShopPerformance {
  shopId: string;
  shopName: string;
  revenue: number;
  expenses: number;
  profit: number;
  profitMargin: number;
  employeeCount: number;
  inventoryValue: number;
}

// Theme Types
export interface ThemeContextType {
  theme: 'light' | 'dark';
  toggleTheme: () => void;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'select' | 'textarea';
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
}
